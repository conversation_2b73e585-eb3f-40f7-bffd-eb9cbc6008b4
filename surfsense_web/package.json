{"name": "surfsense_web", "version": "0.0.7", "private": true, "description": "SurfSense Frontend", "scripts": {"dev": "next dev --turbopack", "dev:turbo": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "debug": "cross-env NODE_OPTIONS=--inspect next dev --turbopack", "debug:browser": "cross-env NODE_OPTIONS=--inspect next dev --turbopack", "debug:server": "cross-env NODE_OPTIONS=--inspect=0.0.0.0:9229 next dev --turbopack", "postinstall": "fumadocs-mdx", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio"}, "dependencies": {"@ai-sdk/react": "^1.2.12", "@hookform/resolvers": "^4.1.3", "@llamaindex/chat-ui": "^0.5.17", "@number-flow/react": "^0.5.10", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tabler/icons-react": "^3.34.1", "@tanstack/react-table": "^8.21.3", "@types/mdx": "^2.0.13", "@types/react-syntax-highlighter": "^15.5.13", "ai": "^4.3.19", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^17.2.3", "drizzle-orm": "^0.44.5", "emblor": "^1.4.8", "fumadocs-core": "^15.6.6", "fumadocs-mdx": "^11.7.1", "fumadocs-ui": "^15.6.6", "geist": "^1.4.2", "lucide-react": "^0.477.0", "motion": "^12.23.22", "next": "^15.4.4", "next-themes": "^0.4.6", "pg": "^8.16.3", "postgres": "^3.4.7", "react": "^19.1.0", "react-day-picker": "^9.8.1", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.61.1", "react-json-view": "^1.21.3", "react-json-view-lite": "^2.4.1", "react-markdown": "^10.1.0", "react-rough-notation": "^1.0.5", "react-syntax-highlighter": "^15.6.1", "react-wrap-balancer": "^1.1.1", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.1", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.76"}, "devDependencies": {"@biomejs/biome": "2.1.2", "@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@types/canvas-confetti": "^1.9.0", "@types/node": "^20.19.9", "@types/pg": "^8.15.5", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "cross-env": "^7.0.3", "drizzle-kit": "^0.31.5", "eslint": "^9.32.0", "eslint-config-next": "15.2.0", "tailwindcss": "^4.1.11", "tsx": "^4.20.6", "typescript": "^5.8.3"}}