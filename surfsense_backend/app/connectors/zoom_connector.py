"""
Zoom connector for fetching meeting recordings, transcripts, and chat messages.
"""

import json
import logging
import time
from typing import Any

import httpx
from dateutil.parser import isoparse

from app.schemas.zoom_auth_credentials import ZoomAuthCredentialsBase

logger = logging.getLogger(__name__)


class ZoomConnector:
    """
    Connector for interacting with Zoom API using OAuth 2.0 credentials.
    """

    def __init__(self, credentials: ZoomAuthCredentialsBase):
        """
        Initialize the ZoomConnector with OAuth credentials.

        Args:
            credentials: Zoom OAuth credentials
        """
        self.credentials = credentials
        self.base_url = "https://api.zoom.us/v2"
        self._client = None

    @property
    def client(self) -> httpx.Client:
        """Get or create HTTP client with authentication headers."""
        if self._client is None:
            self._client = httpx.Client(
                headers={
                    "Authorization": f"Bearer {self.credentials.access_token}",
                    "Content-Type": "application/json",
                },
                timeout=30.0,
            )
        return self._client

    def close(self):
        """Close the HTTP client."""
        if self._client:
            self._client.close()
            self._client = None

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

    def _make_request(
        self,
        method: str,
        endpoint: str,
        params: dict[str, Any] | None = None,
        data: dict[str, Any] | None = None,
    ) -> tuple[dict[str, Any] | None, str | None]:
        """
        Make HTTP request to Zoom API.

        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint
            params: Query parameters
            data: Request body data

        Returns:
            Tuple of (response_data, error_message)
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        try:
            response = self.client.request(
                method=method,
                url=url,
                params=params,
                json=data,
            )
            
            if response.status_code == 429:
                # Rate limited, wait and retry
                retry_after = int(response.headers.get("Retry-After", 60))
                logger.warning(f"Rate limited, waiting {retry_after} seconds")
                time.sleep(retry_after)
                return self._make_request(method, endpoint, params, data)
            
            if response.status_code >= 400:
                error_msg = f"HTTP {response.status_code}: {response.text}"
                logger.error(f"Zoom API error: {error_msg}")
                return None, error_msg
            
            return response.json(), None
            
        except httpx.RequestError as e:
            error_msg = f"Request error: {str(e)}"
            logger.error(error_msg)
            return None, error_msg
        except json.JSONDecodeError as e:
            error_msg = f"JSON decode error: {str(e)}"
            logger.error(error_msg)
            return None, error_msg

    def get_user_recordings(
        self,
        user_id: str = "me",
        page_size: int = 30,
        next_page_token: str | None = None,
        from_date: str | None = None,
        to_date: str | None = None,
        mc: str = "false",  # Include meeting chat
        trash: bool = False,
    ) -> tuple[dict[str, Any] | None, str | None]:
        """
        Get recordings for a user.

        Args:
            user_id: User ID or "me" for current user
            page_size: Number of records per page (max 300)
            next_page_token: Token for pagination
            from_date: Start date (YYYY-MM-DD)
            to_date: End date (YYYY-MM-DD)
            mc: Include meeting chat ("true" or "false")
            trash: Include recordings from trash

        Returns:
            Tuple of (response_data, error_message)
        """
        params = {
            "page_size": min(page_size, 300),
            "mc": mc,
            "trash": str(trash).lower(),
        }
        
        if next_page_token:
            params["next_page_token"] = next_page_token
        if from_date:
            params["from"] = from_date
        if to_date:
            params["to"] = to_date
            
        return self._make_request("GET", f"/users/{user_id}/recordings", params=params)

    def get_meeting_recordings(
        self,
        meeting_uuid: str,
        include_fields: str | None = None,
        ttl: int | None = None,
    ) -> tuple[dict[str, Any] | None, str | None]:
        """
        Get recordings for a specific meeting.

        Args:
            meeting_uuid: Meeting UUID
            include_fields: Additional fields to include
            ttl: Time to live for download URLs in seconds

        Returns:
            Tuple of (response_data, error_message)
        """
        params = {}
        if include_fields:
            params["include_fields"] = include_fields
        if ttl:
            params["ttl"] = ttl
            
        return self._make_request("GET", f"/meetings/{meeting_uuid}/recordings", params=params)

    def get_meeting_transcript(
        self,
        meeting_uuid: str,
        recording_id: str,
    ) -> tuple[dict[str, Any] | None, str | None]:
        """
        Get transcript for a specific meeting recording.

        Args:
            meeting_uuid: Meeting UUID
            recording_id: Recording ID

        Returns:
            Tuple of (response_data, error_message)
        """
        return self._make_request("GET", f"/meetings/{meeting_uuid}/recordings/{recording_id}/transcript")

    def get_all_user_recordings(
        self,
        user_id: str = "me",
        max_recordings: int = 1000,
        from_date: str | None = None,
        to_date: str | None = None,
    ) -> tuple[list[dict[str, Any]], str | None]:
        """
        Get all recordings for a user with pagination.

        Args:
            user_id: User ID or "me" for current user
            max_recordings: Maximum number of recordings to fetch
            from_date: Start date (YYYY-MM-DD)
            to_date: End date (YYYY-MM-DD)

        Returns:
            Tuple of (recordings_list, error_message)
        """
        all_recordings = []
        next_page_token = None
        fetched_count = 0
        
        while fetched_count < max_recordings:
            page_size = min(300, max_recordings - fetched_count)
            
            response_data, error = self.get_user_recordings(
                user_id=user_id,
                page_size=page_size,
                next_page_token=next_page_token,
                from_date=from_date,
                to_date=to_date,
                mc="true",  # Include meeting chat
            )
            
            if error:
                return all_recordings, error
                
            if not response_data or "meetings" not in response_data:
                break
                
            meetings = response_data["meetings"]
            all_recordings.extend(meetings)
            fetched_count += len(meetings)
            
            next_page_token = response_data.get("next_page_token")
            if not next_page_token:
                break
                
        return all_recordings, None
