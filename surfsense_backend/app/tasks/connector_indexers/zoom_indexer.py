"""
Zoom connector indexer.
"""

import json
import logging
from datetime import datetime
from typing import Any

from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession

from app.config import config
from app.connectors.zoom_connector import ZoomConnector
from app.db import Document, DocumentType, SearchSourceConnectorType
from app.routes.zoom_add_connector_route import refresh_zoom_token
from app.schemas.zoom_auth_credentials import ZoomAuthCredentialsBase
from app.services.llm_service import get_user_long_context_llm
from app.services.task_logging_service import TaskLoggingService
from app.utils.document_converters import (
    create_document_chunks,
    generate_content_hash,
    generate_document_summary,
)

logger = logging.getLogger(__name__)


async def index_zoom_meetings(
    session: AsyncSession,
    connector_id: int,
    search_space_id: int,
    user_id: str,
    start_date: str | None = None,
    end_date: str | None = None,
    max_meetings: int = 1000,
    update_last_indexed: bool = True,
) -> tuple[int, str | None]:
    """
    Index Zoom meetings for a given connector.

    Args:
        session: Database session
        connector_id: ID of the Zoom connector
        search_space_id: ID of the search space to store documents in
        user_id: ID of the user
        start_date: Start date for filtering meetings (YYYY-MM-DD)
        end_date: End date for filtering meetings (YYYY-MM-DD)
        max_meetings: Maximum number of meetings to fetch
        update_last_indexed: Whether to update the last_indexed_at timestamp

    Returns:
        Tuple of (indexed_count, error_message)
    """
    task_logger = TaskLoggingService(
        session=session,
        user_id=user_id,
        task_type="zoom_indexing",
        connector_id=connector_id,
    )

    try:
        await task_logger.log_info("Starting Zoom meetings indexing")

        # Get connector from database
        from app.db import SearchSourceConnector
        from sqlalchemy import select

        connector_result = await session.execute(
            select(SearchSourceConnector).where(SearchSourceConnector.id == connector_id)
        )
        connector = connector_result.scalars().first()

        if not connector:
            error_msg = f"Connector with ID {connector_id} not found"
            await task_logger.log_error(error_msg)
            return 0, error_msg

        if connector.connector_type != SearchSourceConnectorType.ZOOM_CONNECTOR:
            error_msg = f"Connector {connector_id} is not a Zoom connector"
            await task_logger.log_error(error_msg)
            return 0, error_msg

        # Parse credentials
        try:
            credentials = ZoomAuthCredentialsBase.from_dict(connector.config)
        except Exception as e:
            error_msg = f"Failed to parse Zoom credentials: {str(e)}"
            await task_logger.log_error(error_msg)
            return 0, error_msg

        # Check if token needs refresh
        if credentials.is_expired and credentials.is_refreshable:
            await task_logger.log_info("Access token expired, refreshing...")
            refreshed_credentials = await refresh_zoom_token(session, connector)
            if refreshed_credentials:
                credentials = refreshed_credentials
                await task_logger.log_info("Successfully refreshed access token")
            else:
                error_msg = "Failed to refresh access token"
                await task_logger.log_error(error_msg)
                return 0, error_msg

        # Initialize Zoom connector
        with ZoomConnector(credentials) as zoom_client:
            await task_logger.log_info("Fetching Zoom meetings...")

            # Get all recordings for the user
            meetings, error = zoom_client.get_all_user_recordings(
                user_id="me",
                max_recordings=max_meetings,
                from_date=start_date,
                to_date=end_date,
            )

            if error:
                error_msg = f"Failed to fetch Zoom meetings: {error}"
                await task_logger.log_error(error_msg)
                return 0, error_msg

            await task_logger.log_info(f"Found {len(meetings)} meetings to process")

            indexed_count = 0
            for meeting in meetings:
                try:
                    # Process each meeting
                    meeting_data = await process_meeting_data(
                        zoom_client, meeting, task_logger
                    )
                    
                    if meeting_data:
                        # Create document
                        await create_meeting_document(
                            session, meeting_data, search_space_id, user_id, task_logger
                        )
                        indexed_count += 1

                except Exception as e:
                    await task_logger.log_error(f"Error processing meeting {meeting.get('id', 'unknown')}: {str(e)}")
                    continue

            # Update last indexed timestamp
            if update_last_indexed:
                connector.last_indexed_at = datetime.utcnow()
                await session.commit()

            await task_logger.log_info(f"Successfully indexed {indexed_count} meetings")
            return indexed_count, None

    except SQLAlchemyError as e:
        await session.rollback()
        error_msg = f"Database error during Zoom indexing: {str(e)}"
        await task_logger.log_error(error_msg)
        return 0, error_msg

    except Exception as e:
        error_msg = f"Unexpected error during Zoom indexing: {str(e)}"
        await task_logger.log_error(error_msg)
        return 0, error_msg


async def process_meeting_data(
    zoom_client: ZoomConnector,
    meeting: dict[str, Any],
    task_logger: TaskLoggingService,
) -> dict[str, Any] | None:
    """
    Process meeting data to extract recordings, transcripts, and chat.

    Args:
        zoom_client: Zoom connector client
        meeting: Meeting data from Zoom API
        task_logger: Task logger

    Returns:
        Processed meeting data or None if failed
    """
    try:
        meeting_id = meeting.get("id")
        meeting_uuid = meeting.get("uuid")
        
        if not meeting_uuid:
            await task_logger.log_warning(f"Meeting {meeting_id} has no UUID, skipping")
            return None

        # Get detailed recording information
        recording_data, error = zoom_client.get_meeting_recordings(meeting_uuid)
        if error:
            await task_logger.log_warning(f"Failed to get recordings for meeting {meeting_id}: {error}")
            return None

        # Extract meeting metadata
        meeting_data = {
            "meeting_id": meeting_id,
            "meeting_uuid": meeting_uuid,
            "meeting_topic": meeting.get("topic", ""),
            "meeting_agenda": meeting.get("agenda", ""),
            "created_at": meeting.get("start_time"),
            "meeting_start": meeting.get("start_time"),
            "meeting_end": meeting.get("end_time"),
            "duration": meeting.get("duration", 0),
            "host_id": meeting.get("host_id", ""),
            "transcript_text": "",
            "transcript_segments": [],
            "chat_messages": [],
        }

        # Process recording files
        recording_files = recording_data.get("recording_files", [])
        
        for file_data in recording_files:
            file_type = file_data.get("file_type", "").upper()
            
            if file_type == "TRANSCRIPT":
                # Get transcript content
                recording_id = file_data.get("id")
                if recording_id:
                    transcript_data, transcript_error = zoom_client.get_meeting_transcript(
                        meeting_uuid, recording_id
                    )
                    if transcript_data and not transcript_error:
                        meeting_data["transcript_text"] = transcript_data.get("transcript", "")
                        meeting_data["transcript_segments"] = transcript_data.get("transcript_timeline", [])
            
            elif file_type == "CHAT":
                # Chat messages are usually in the recording files
                chat_content = file_data.get("download_url", "")
                if chat_content:
                    # Note: In a real implementation, you'd download and parse the chat file
                    # For now, we'll store the download URL
                    meeting_data["chat_messages"] = [{"note": f"Chat file available at: {chat_content}"}]

        return meeting_data

    except Exception as e:
        await task_logger.log_error(f"Error processing meeting data: {str(e)}")
        return None


async def create_meeting_document(
    session: AsyncSession,
    meeting_data: dict[str, Any],
    search_space_id: int,
    user_id: str,
    task_logger: TaskLoggingService,
) -> None:
    """
    Create a document from meeting data.

    Args:
        session: Database session
        meeting_data: Processed meeting data
        search_space_id: Search space ID
        user_id: User ID
        task_logger: Task logger
    """
    try:
        # Create document content
        content_parts = []
        
        # Add meeting info
        content_parts.append(f"Meeting: {meeting_data['meeting_topic']}")
        if meeting_data.get("meeting_agenda"):
            content_parts.append(f"Agenda: {meeting_data['meeting_agenda']}")
        
        content_parts.append(f"Date: {meeting_data.get('meeting_start', '')}")
        content_parts.append(f"Duration: {meeting_data.get('duration', 0)} minutes")
        
        # Add transcript
        if meeting_data.get("transcript_text"):
            content_parts.append("\n--- TRANSCRIPT ---")
            content_parts.append(meeting_data["transcript_text"])
        
        # Add chat messages
        if meeting_data.get("chat_messages"):
            content_parts.append("\n--- CHAT MESSAGES ---")
            for msg in meeting_data["chat_messages"]:
                if isinstance(msg, dict):
                    sender = msg.get("sender", "Unknown")
                    message = msg.get("message_text", msg.get("note", ""))
                    time_str = msg.get("time", "")
                    content_parts.append(f"[{time_str}] {sender}: {message}")

        content = "\n".join(content_parts)
        
        if not content.strip():
            await task_logger.log_warning(f"No content found for meeting {meeting_data['meeting_id']}")
            return

        # Generate content hash
        content_hash = generate_content_hash(content)

        # Check if document already exists
        from sqlalchemy import select
        existing_doc = await session.execute(
            select(Document).where(Document.content_hash == content_hash)
        )
        if existing_doc.scalars().first():
            await task_logger.log_info(f"Meeting {meeting_data['meeting_id']} already indexed")
            return

        # Create document title
        title = f"Zoom Meeting: {meeting_data['meeting_topic']} ({meeting_data.get('meeting_start', '')})"

        # Generate summary
        llm = get_user_long_context_llm(user_id)
        summary = await generate_document_summary(content, llm)

        # Create document metadata
        metadata = {
            "meeting_id": meeting_data["meeting_id"],
            "meeting_uuid": meeting_data["meeting_uuid"],
            "meeting_topic": meeting_data["meeting_topic"],
            "meeting_agenda": meeting_data.get("meeting_agenda"),
            "meeting_start": meeting_data.get("meeting_start"),
            "meeting_end": meeting_data.get("meeting_end"),
            "duration": meeting_data.get("duration"),
            "host_id": meeting_data.get("host_id"),
            "has_transcript": bool(meeting_data.get("transcript_text")),
            "has_chat": bool(meeting_data.get("chat_messages")),
            "summary": summary,
        }

        # Create document
        document = Document(
            title=title,
            document_type=DocumentType.ZOOM_CONNECTOR,
            document_metadata=metadata,
            content=content,
            content_hash=content_hash,
            search_space_id=search_space_id,
        )

        session.add(document)
        await session.flush()  # Get document ID

        # Create chunks
        await create_document_chunks(session, document, config.chunker_instance)
        
        await task_logger.log_info(f"Created document for meeting: {meeting_data['meeting_topic']}")

    except Exception as e:
        await task_logger.log_error(f"Error creating document: {str(e)}")
        raise
