"""
Zoom connector OAuth routes for authentication and setup.
"""

import json
import logging
import urllib.parse
from datetime import UTC, datetime, timedelta

import httpx
from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.config import config
from app.db import SearchSourceConnector, SearchSourceConnectorType, get_async_session
from app.schemas.zoom_auth_credentials import ZoomAuthCredentialsBase
from app.users import User, current_active_user

logger = logging.getLogger(__name__)

router = APIRouter()


AUTHORIZATION_URL = "https://zoom.us/oauth/authorize"
TOKEN_URL = "https://zoom.us/oauth/token"


@router.get("/auth/zoom/connector/add/")
async def connect_zoom(space_id: int, user: User = Depends(current_active_user)):
    """
    Initiate Zoom OAuth flow.

    Args:
        space_id: The search space ID
        user: Current authenticated user

    Returns:
        Authorization URL for redirect
    """
    try:
        if not space_id:
            raise HTTPException(status_code=400, detail="space_id is required")

        if not config.ZOOM_CLIENT_ID:
            raise HTTPException(status_code=500, detail="Zoom OAuth not configured.")

        # Generate state parameter
        state_payload = json.dumps(
            {
                "space_id": space_id,
                "user_id": str(user.id),
            }
        )
        encoded_state = urllib.parse.quote(state_payload)

        # Zoom OAuth scopes for recordings and meetings
        scopes = [
            "recording:read",
            "meeting:read",
            "user:read",
        ]

        # Build authorization URL
        auth_params = {
            "response_type": "code",
            "client_id": config.ZOOM_CLIENT_ID,
            "redirect_uri": config.ZOOM_REDIRECT_URI,
            "scope": " ".join(scopes),
            "state": encoded_state,
        }

        auth_url = AUTHORIZATION_URL + "?" + urllib.parse.urlencode(auth_params)

        return {"auth_url": auth_url}

    except Exception as e:
        logger.error(f"Error initiating Zoom OAuth: {e!s}")
        raise HTTPException(
            status_code=500, detail="Failed to initiate OAuth flow"
        ) from e


@router.get("/auth/zoom/connector/callback/")
async def zoom_oauth_callback(
    request: Request,
    code: str | None = None,
    state: str | None = None,
    error: str | None = None,
    session: AsyncSession = Depends(get_async_session),
):
    """
    Handle Zoom OAuth callback.

    Args:
        request: FastAPI request object
        code: Authorization code from Zoom
        state: State parameter
        error: Error from OAuth provider
        session: Database session

    Returns:
        Redirect response
    """
    try:
        if error:
            logger.error(f"OAuth error: {error}")
            raise HTTPException(status_code=400, detail=f"OAuth error: {error}")

        if not code or not state:
            raise HTTPException(
                status_code=400, detail="Missing code or state parameter"
            )

        # Decode state
        try:
            state_data = json.loads(urllib.parse.unquote(state))
            space_id = state_data["space_id"]
            user_id = state_data["user_id"]
        except (json.JSONDecodeError, KeyError) as e:
            logger.error(f"Invalid state parameter: {e}")
            raise HTTPException(
                status_code=400, detail="Invalid state parameter"
            ) from e

        # Exchange code for tokens
        token_data = await exchange_code_for_tokens(code)
        if not token_data:
            raise HTTPException(
                status_code=400, detail="Failed to exchange code for tokens"
            )

        # Create credentials object
        credentials = ZoomAuthCredentialsBase(
            access_token=token_data["access_token"],
            refresh_token=token_data.get("refresh_token"),
            token_type=token_data.get("token_type", "Bearer"),
            expires_in=token_data.get("expires_in"),
            expires_at=datetime.now(UTC)
            + timedelta(seconds=token_data.get("expires_in", 3600)),
            scope=token_data.get("scope"),
        )

        # Save or update connector
        await save_zoom_connector(session, user_id, credentials)

        # Redirect to success page
        redirect_url = (
            f"{config.FRONTEND_URL}/dashboard/{space_id}/connectors?zoom_connected=true"
        )
        return {"redirect_url": redirect_url}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in Zoom OAuth callback: {e!s}")
        raise HTTPException(status_code=500, detail="OAuth callback failed") from e


async def exchange_code_for_tokens(code: str) -> dict | None:
    """
    Exchange authorization code for access tokens.

    Args:
        code: Authorization code

    Returns:
        Token data or None if failed
    """
    try:
        token_data = {
            "grant_type": "authorization_code",
            "code": code,
            "redirect_uri": config.ZOOM_REDIRECT_URI,
        }

        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
        }

        # Use basic auth with client credentials
        auth = (config.ZOOM_CLIENT_ID, config.ZOOM_CLIENT_SECRET)

        async with httpx.AsyncClient() as client:
            response = await client.post(
                TOKEN_URL,
                data=token_data,
                headers=headers,
                auth=auth,
                timeout=30.0,
            )

            if response.status_code != 200:
                logger.error(
                    f"Token exchange failed: {response.status_code} - {response.text}"
                )
                return None

            return response.json()

    except Exception as e:
        logger.error(f"Error exchanging code for tokens: {e!s}")
        return None


async def save_zoom_connector(
    session: AsyncSession,
    user_id: str,
    credentials: ZoomAuthCredentialsBase,
) -> None:
    """
    Save or update Zoom connector in database.

    Args:
        session: Database session
        user_id: User ID
        credentials: Zoom credentials
    """
    try:
        # Check if connector already exists
        existing_connector_result = await session.execute(
            select(SearchSourceConnector).where(
                SearchSourceConnector.user_id == user_id,
                SearchSourceConnector.connector_type
                == SearchSourceConnectorType.ZOOM_CONNECTOR,
            )
        )
        existing_connector = existing_connector_result.scalars().first()

        if existing_connector:
            # Update existing connector
            existing_connector.config = credentials.to_dict()
            existing_connector.name = "Zoom Connector"
            existing_connector.is_indexable = True
            logger.info(f"Updated existing Zoom connector for user {user_id}")
        else:
            # Create new connector
            new_connector = SearchSourceConnector(
                name="Zoom Connector",
                connector_type=SearchSourceConnectorType.ZOOM_CONNECTOR,
                is_indexable=True,
                config=credentials.to_dict(),
                user_id=user_id,
            )
            session.add(new_connector)
            logger.info(f"Created new Zoom connector for user {user_id}")

        await session.commit()

    except Exception as e:
        await session.rollback()
        logger.error(f"Error saving Zoom connector: {e!s}")
        raise


async def refresh_zoom_token(
    session: AsyncSession,
    connector: SearchSourceConnector,
) -> ZoomAuthCredentialsBase | None:
    """
    Refresh Zoom access token.

    Args:
        session: Database session
        connector: Zoom connector

    Returns:
        Updated credentials or None if failed
    """
    try:
        credentials = ZoomAuthCredentialsBase.from_dict(connector.config)

        if not credentials.refresh_token:
            logger.error("No refresh token available")
            return None

        refresh_data = {
            "grant_type": "refresh_token",
            "refresh_token": credentials.refresh_token,
        }

        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
        }

        async with httpx.AsyncClient() as client:
            response = await client.post(
                TOKEN_URL,
                data=refresh_data,
                headers=headers,
            )

            if response.status_code != 200:
                logger.error(
                    f"Token refresh failed: {response.status_code} - {response.text}"
                )
                return None

            token_data = response.json()

            # Update credentials
            credentials.access_token = token_data["access_token"]
            if "refresh_token" in token_data:
                credentials.refresh_token = token_data["refresh_token"]
            credentials.expires_in = token_data.get("expires_in")
            credentials.expires_at = datetime.now(UTC) + timedelta(
                seconds=token_data.get("expires_in", 3600)
            )

            # Save updated credentials
            connector.config = credentials.to_dict()
            await session.commit()

            logger.info("Successfully refreshed Zoom token")
            return credentials

    except Exception as e:
        logger.error(f"Error refreshing Zoom token: {e!s}")
        return None
