"""Add ZOOM_CONNECTOR to enums

Revision ID: 20
Revises: 19
"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "20"
down_revision = "19"
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Upgrade schema - add <PERSON><PERSON>OM_CONNECTOR to enums."""
    # Add to searchsourceconnectortype enum
    op.execute(
        """
    DO $$
    BEGIN
        IF NOT EXISTS (
            SELECT 1 FROM pg_type t
            JOIN pg_enum e ON t.oid = e.enumtypid
            WHERE t.typname = 'searchsourceconnectortype' AND e.enumlabel = 'ZOOM_CONNECTOR'
        ) THEN
            ALTER TYPE searchsourceconnectortype ADD VALUE 'ZOOM_CONNECTOR';
        END IF;
    END
    $$;
    """
    )

    # Add to documenttype enum
    op.execute(
        """
    DO $$
    BEGIN
        IF NOT EXISTS (
            SELECT 1 FROM pg_type t
            JOIN pg_enum e ON t.oid = e.enumtypid
            WHERE t.typname = 'documenttype' AND e.enumlabel = 'ZOOM_CONNECTOR'
        ) THEN
            ALTER TYPE documenttype ADD VALUE 'ZOOM_CONNECTOR';
        END IF;
    END
    $$;
    """
    )


def downgrade() -> None:
    """Downgrade schema - remove ZOOM_CONNECTOR from enums."""
    pass
